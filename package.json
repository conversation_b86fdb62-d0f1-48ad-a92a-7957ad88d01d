{"name": "clinic-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:clinic-frontend": "node dist/clinic-frontend/server/server.mjs"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.10", "@auth0/angular-jwt": "^5.2.0", "@ngrx/effects": "^19.2.0", "@ngrx/entity": "^19.2.0", "@ngrx/router-store": "^19.2.0", "@ngrx/store": "^19.2.0", "@ngrx/store-devtools": "^19.2.0", "@tailwindcss/postcss": "^4.1.7", "express": "^4.18.2", "rxjs": "~7.8.0", "tailwindcss": "^3.4.17", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "autoprefixer": "^10.4.21", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "typescript": "~5.7.2"}}