import { BaseModel } from './base.model';

export interface MedicalRecord extends BaseModel {
  id: string;
  patientId: string;
  doctorId: string;
  appointmentId?: string;
  recordDate: string;
  diagnosis: string;
  treatment: string;
  prescription?: string;
  notes?: string;
  followUpDate?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface MedicalRecordListResponse {
  medicalRecords: MedicalRecord[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
}

export interface MedicalRecordRequest {
  patientId: string;
  doctorId: string;
  appointmentId?: string;
  recordDate: string;
  diagnosis: string;
  treatment: string;
  prescription?: string;
  notes?: string;
  followUpDate?: string;
} 