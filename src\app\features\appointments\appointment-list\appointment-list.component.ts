import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AppointmentService } from '../services/appointment.service';
import { Appointment } from '../models/appointment.model';
import { PaginatedResponse } from '../../../core/models/api-response.model';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

@Component({
  selector: 'app-appointment-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './appointment-list.component.html',
  styleUrls: ['./appointment-list.component.scss']
})
export class AppointmentListComponent implements OnInit {
  appointments: Appointment[] = [];
  loading = false;
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  searchTerm = '';
  searchTermChanged = new Subject<string>();
  Math = Math;

  constructor(private appointmentService: AppointmentService) {
    this.searchTermChanged
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(term => {
        this.searchTerm = term;
        this.currentPage = 1;
        this.loadAppointments();
      });
  }

  ngOnInit(): void {
    this.loadAppointments();
  }

  loadAppointments(): void {
    this.loading = true;
    this.appointmentService.getPaginatedAppointments(this.currentPage, this.pageSize, this.searchTerm)
      .subscribe({
        next: (response: PaginatedResponse<Appointment>) => {
          this.appointments = response.items;
          this.totalItems = response.totalCount;
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error loading appointments:', error);
          this.loading = false;
        }
      });
  }

  onSearch(searchTerm: string): void {
    this.searchTermChanged.next(searchTerm);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAppointments();
  }

  onDelete(id: string): void {
    if (confirm('Are you sure you want to delete this appointment?')) {
      this.loading = true;
      this.appointmentService.deleteAppointment(id).subscribe({
        next: () => {
          this.loadAppointments();
        },
        error: (error: any) => {
          console.error('Error deleting appointment:', error);
          this.loading = false;
        }
      });
    }
  }
} 