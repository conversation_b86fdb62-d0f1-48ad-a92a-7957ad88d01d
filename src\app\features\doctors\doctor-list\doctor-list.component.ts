import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DoctorService } from '../services/doctor.service';
import { Doctor } from '../models/doctor.model';
import { PaginatedResponse } from '../../../core/models/api-response.model';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

@Component({
  selector: 'app-doctor-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './doctor-list.component.html',
  styleUrls: ['./doctor-list.component.scss']
})
export class DoctorListComponent implements OnInit {
  doctors: Doctor[] = [];
  loading = false;
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  searchTerm = '';
  searchTermChanged = new Subject<string>();
  Math = Math; // Make Math available in the template

  constructor(
    private doctorService: DoctorService,
    public router: Router
  ) {
    this.searchTermChanged
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(term => {
        this.searchTerm = term;
        this.currentPage = 1;
        this.loadDoctors();
      });
  }

  ngOnInit(): void {
    this.loadDoctors();
  }

  loadDoctors(): void {
    this.loading = true;
    this.doctorService.getPaginatedDoctors(this.currentPage, this.pageSize, this.searchTerm)
      .subscribe({
        next: (response: PaginatedResponse<Doctor>) => {
          this.doctors = response.items;
          this.totalItems = response.totalCount;
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error loading doctors:', error);
          this.loading = false;
        }
      });
  }

  onSearch(searchTerm: string): void {
    this.searchTermChanged.next(searchTerm);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadDoctors();
  }

  onDelete(id: string): void {
    if (confirm('Are you sure you want to delete this doctor?')) {
      this.loading = true;
      this.doctorService.delete(id).subscribe({
        next: () => {
          this.loadDoctors();
        },
        error: (error: any) => {
          console.error('Error deleting doctor:', error);
          this.loading = false;
        }
      });
    }
  }
} 