import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Doctor } from '../models/doctor.model';
import { PaginationQuery, PaginatedResponse } from '../../../core/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class DoctorService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/doctors`;

  getDoctors(): Observable<Doctor[]> {
    return this.http.get<Doctor[]>(this.apiUrl);
  }

  getDoctor(id: string): Observable<Doctor> {
    return this.http.get<Doctor>(`${this.apiUrl}/${id}`);
  }

  createDoctor(doctor: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Observable<Doctor> {
    return this.http.post<Doctor>(this.apiUrl, doctor);
  }

  updateDoctor(id: string, doctor: Partial<Doctor>): Observable<Doctor> {
    return this.http.put<Doctor>(`${this.apiUrl}/${id}`, doctor);
  }

  deleteDoctor(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getPaginatedDoctors(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<PaginatedResponse<Doctor>> {
    const query: PaginationQuery = {
      pageNumber,
      pageSize,
      searchTerm
    };
    return this.http.get<PaginatedResponse<Doctor>>(`${this.apiUrl}/paginated`, { params: query as any });
  }

  // Alias for deleteDoctor to maintain compatibility
  delete(id: string): Observable<void> {
    return this.deleteDoctor(id);
  }
} 