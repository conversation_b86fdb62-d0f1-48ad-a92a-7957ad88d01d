import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AppointmentService } from '../services/appointment.service';
import { DoctorService } from '../../doctors/services/doctor.service';
import { PatientService } from '../../patients/services/patient.service';
import { Appointment, AppointmentStatus } from '../models/appointment.model';
import { Doctor } from '../../doctors/models/doctor.model';
import { Patient } from '../../patients/models/patient.model';

@Component({
  selector: 'app-appointment-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './appointment-form.component.html',
  styleUrls: ['./appointment-form.component.scss']
})
export class AppointmentFormComponent implements OnInit {
  appointmentForm: FormGroup;
  isEditMode = false;
  appointmentId: string | null = null;
  loading = false;
  error: string | null = null;
  doctors: Doctor[] = [];
  patients: Patient[] = [];
  AppointmentStatus = AppointmentStatus;

  constructor(
    private fb: FormBuilder,
    private appointmentService: AppointmentService,
    private doctorService: DoctorService,
    private patientService: PatientService,
    private route: ActivatedRoute,
    public router: Router
  ) {
    this.appointmentForm = this.fb.group({
      patientId: ['', Validators.required],
      doctorId: ['', Validators.required],
      date: ['', Validators.required],
      startTime: ['', Validators.required],
      endTime: ['', Validators.required],
      status: [AppointmentStatus.Scheduled, Validators.required],
      notes: ['']
    });
  }

  ngOnInit(): void {
    this.loadDoctors();
    this.loadPatients();
    this.appointmentId = this.route.snapshot.paramMap.get('id');
    if (this.appointmentId) {
      this.isEditMode = true;
      this.loadAppointment();
    }
  }

  private loadDoctors(): void {
    this.doctorService.getDoctors().subscribe({
      next: (doctors) => {
        this.doctors = doctors;
      },
      error: (error) => {
        console.error('Error loading doctors:', error);
        this.error = 'Failed to load doctors';
      }
    });
  }

  private loadPatients(): void {
    this.patientService.getPatients().subscribe({
      next: (patients) => {
        this.patients = patients;
      },
      error: (error) => {
        console.error('Error loading patients:', error);
        this.error = 'Failed to load patients';
      }
    });
  }

  private loadAppointment(): void {
    if (!this.appointmentId) return;
    
    this.loading = true;
    this.error = null;
    
    this.appointmentService.getAppointment(this.appointmentId).subscribe({
      next: (appointment) => {
        this.appointmentForm.patchValue(appointment);
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load appointment data';
        this.loading = false;
        console.error('Error loading appointment:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      this.loading = true;
      this.error = null;
      const appointmentData = this.appointmentForm.value;

      const request$ = this.isEditMode && this.appointmentId
        ? this.appointmentService.updateAppointment(this.appointmentId, appointmentData)
        : this.appointmentService.createAppointment(appointmentData);

      request$.subscribe({
        next: () => {
          this.loading = false;
          this.router.navigate(['/appointments']);
        },
        error: (error) => {
          this.error = 'Failed to save appointment data';
          this.loading = false;
          console.error('Error saving appointment:', error);
        }
      });
    }
  }
} 