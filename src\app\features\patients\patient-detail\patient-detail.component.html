<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Patient Details</h1>
    <a (click)="goBack()" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md flex items-center cursor-pointer">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
      </svg>
      Back to Patients
    </a>
  </div>

  <div class="bg-white rounded-lg shadow mb-6">
    <div *ngIf="loading" class="flex justify-center items-center p-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
    </div>

    <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      <span class="block sm:inline">{{ error }}</span>
    </div>

    <div *ngIf="patient && !loading" class="p-6">
      <div class="flex justify-between items-start mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">{{ patient.firstName }} {{ patient.lastName }}</h2>
        <div class="flex space-x-2">
          <button (click)="goToEdit()" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
            Edit
          </button>
          <button (click)="deletePatient()" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            Delete
          </button>
        </div>
      </div>

      <!-- Personal Information -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Personal Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">Date of Birth</p>
            <p class="text-base text-gray-900">{{ patient.dateOfBirth | date:'mediumDate' }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Gender</p>
            <p class="text-base text-gray-900">{{ patient.gender }}</p>
          </div>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Contact Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">Email</p>
            <p class="text-base text-gray-900">{{ patient.email }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Phone</p>
            <p class="text-base text-gray-900">{{ patient.phone }}</p>
          </div>
          <div class="md:col-span-2">
            <p class="text-sm font-medium text-gray-500">Address</p>
            <p class="text-base text-gray-900">{{ patient.address }}, {{ patient.city }}, {{ patient.state }} {{ patient.postalCode }}</p>
          </div>
        </div>
      </div>

      <!-- Emergency Contact -->
      <div *ngIf="patient.emergencyContactName || patient.emergencyContactPhone" class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Emergency Contact</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngIf="patient.emergencyContactName">
            <p class="text-sm font-medium text-gray-500">Name</p>
            <p class="text-base text-gray-900">{{ patient.emergencyContactName }}</p>
          </div>
          <div *ngIf="patient.emergencyContactPhone">
            <p class="text-sm font-medium text-gray-500">Phone</p>
            <p class="text-base text-gray-900">{{ patient.emergencyContactPhone }}</p>
          </div>
        </div>
      </div>

      <!-- Insurance Information -->
      <div *ngIf="patient.insuranceProvider || patient.insurancePolicyNumber" class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Insurance Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngIf="patient.insuranceProvider">
            <p class="text-sm font-medium text-gray-500">Provider</p>
            <p class="text-base text-gray-900">{{ patient.insuranceProvider }}</p>
          </div>
          <div *ngIf="patient.insurancePolicyNumber">
            <p class="text-sm font-medium text-gray-500">Policy Number</p>
            <p class="text-base text-gray-900">{{ patient.insurancePolicyNumber }}</p>
          </div>
        </div>
      </div>

      <!-- Medical Information -->
      <div *ngIf="patient.medicalHistory || patient.allergies" class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Medical Information</h3>
        <div *ngIf="patient.medicalHistory" class="mb-4">
          <p class="text-sm font-medium text-gray-500">Medical History</p>
          <p class="text-base text-gray-900 whitespace-pre-line">{{ patient.medicalHistory }}</p>
        </div>
        <div *ngIf="patient.allergies">
          <p class="text-sm font-medium text-gray-500">Allergies</p>
          <p class="text-base text-gray-900 whitespace-pre-line">{{ patient.allergies }}</p>
        </div>
      </div>

      <!-- Audit Information -->
      <div class="mt-8 pt-4 border-t border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
          <div>
            <p>Created: {{ patient.createdAt | date:'medium' }}</p>
          </div>
          <div *ngIf="patient.updatedAt">
            <p>Last Updated: {{ patient.updatedAt | date:'medium' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 