import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { AppointmentService } from '../services/appointment.service';
import { Appointment } from '../models/appointment.model';

@Component({
  selector: 'app-appointment-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './appointment-detail.component.html',
  styleUrls: ['./appointment-detail.component.scss']
})
export class AppointmentDetailComponent implements OnInit {
  appointment: Appointment | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private appointmentService: AppointmentService,
    private route: ActivatedRoute,
    public router: Router
  ) {}

  ngOnInit(): void {
    const appointmentId = this.route.snapshot.paramMap.get('id');
    if (appointmentId) {
      this.loadAppointment(appointmentId);
    } else {
      this.error = 'Appointment ID not provided';
      this.loading = false;
    }
  }

  private loadAppointment(id: string): void {
    this.appointmentService.getAppointment(id).subscribe({
      next: (appointment) => {
        this.appointment = appointment;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading appointment:', error);
        this.error = 'Failed to load appointment details';
        this.loading = false;
      }
    });
  }

  onDelete(): void {
    if (this.appointment && confirm('Are you sure you want to delete this appointment?')) {
      this.loading = true;
      this.appointmentService.deleteAppointment(this.appointment.id).subscribe({
        next: () => {
          this.router.navigate(['/appointments']);
        },
        error: (error) => {
          console.error('Error deleting appointment:', error);
          this.error = 'Failed to delete appointment';
          this.loading = false;
        }
      });
    }
  }
} 