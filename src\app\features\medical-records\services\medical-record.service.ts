import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MedicalRecord, MedicalRecordListResponse, MedicalRecordRequest } from '../../../core/models/medical-record.model';
import { BaseHttpService } from '../../../core/services/base-http.service';

@Injectable({
  providedIn: 'root'
})
export class MedicalRecordService extends BaseHttpService<MedicalRecord> {
  constructor(http: HttpClient) {
    super(http, 'medical-records');
  }

  getPaginatedMedicalRecords(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<MedicalRecordListResponse> {
    let params: any = {
      pageNumber,
      pageSize
    };

    if (searchTerm) {
      params.searchTerm = searchTerm;
    }

    return this.http.get<MedicalRecordListResponse>(`${this.apiUrl}/paged`, { params });
  }

  getMedicalRecordsByPatient(patientId: string): Observable<MedicalRecord[]> {
    return this.http.get<MedicalRecord[]>(`${this.apiUrl}/patient/${patientId}`);
  }

  getMedicalRecordsByDoctor(doctorId: string): Observable<MedicalRecord[]> {
    return this.http.get<MedicalRecord[]>(`${this.apiUrl}/doctor/${doctorId}`);
  }

  createMedicalRecord(medicalRecord: MedicalRecordRequest): Observable<MedicalRecord> {
    return this.create(medicalRecord);
  }

  updateMedicalRecord(id: string, medicalRecord: Partial<MedicalRecordRequest>): Observable<MedicalRecord> {
    return this.update(id, medicalRecord);
  }
} 