import { inject } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { UserRole } from '../models/auth.model';

export const roleGuard = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // First check if the user is authenticated
  if (!authService.isAuthenticated()) {
    return router.createUrlTree(['/login'], { queryParams: { returnUrl: state.url } });
  }

  // Check if route has data.roles specified
  const roles = route.data['roles'] as Array<UserRole>;
  if (!roles || roles.length === 0) {
    return true; // No specific roles required
  }

  // Check if user has required role
  if (authService.hasRole(roles)) {
    return true;
  }

  // If not in role, redirect to unauthorized page
  return router.createUrlTree(['/unauthorized']);
}; 