import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { PatientService } from '../services/patient.service';
import { Patient } from '../models/patient.model';

@Component({
  selector: 'app-patient-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="container mx-auto px-4 py-6">
      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>

      <div *ngIf="!loading && patient" class="max-w-3xl mx-auto">
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h1 class="text-2xl font-bold text-gray-900">
                {{ patient.firstName }} {{ patient.lastName }}
              </h1>
              <div class="flex space-x-3">
                <button
                  [routerLink]="['/patients', patient.id, 'edit']"
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Edit
                </button>
                <button
                  (click)="onDelete()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Personal Information -->
              <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.email }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.phoneNumber }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.dateOfBirth | date }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Gender</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.gender }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Address Information -->
              <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Address Information</h2>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.address }}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="error" class="mt-4 text-red-600 text-center">
        {{ error }}
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class PatientDetailComponent implements OnInit {
  patient: Patient | null = null;
  loading = false;
  error = '';

  constructor(private patientService: PatientService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    // Implementation will be added when we have route parameters
  }

  onDelete(): void {
    if (this.patient && confirm('Are you sure you want to delete this patient?')) {
      this.patientService.deletePatient(this.patient.id).subscribe({
        next: () => {
          // Navigation will be added
        },
        error: (error: any) => {
          this.error = 'Failed to delete patient. Please try again.';
          console.error('Error deleting patient:', error);
        }
      });
    }
  }
} 