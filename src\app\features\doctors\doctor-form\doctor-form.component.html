<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">{{ isEditMode ? 'Edit Doctor' : 'Add New Doctor' }}</h1>

  <form [formGroup]="doctorForm" (ngSubmit)="onSubmit()" class="max-w-2xl">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="form-group">
        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
        <input
          type="text"
          id="firstName"
          formControlName="firstName"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>

      <div class="form-group">
        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
        <input
          type="text"
          id="lastName"
          formControlName="lastName"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>

      <div class="form-group">
        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>

      <div class="form-group">
        <label for="phoneNumber" class="block text-sm font-medium text-gray-700">Phone Number</label>
        <input
          type="tel"
          id="phoneNumber"
          formControlName="phoneNumber"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>

      <div class="form-group">
        <label for="specialization" class="block text-sm font-medium text-gray-700">Specialization</label>
        <input
          type="text"
          id="specialization"
          formControlName="specialization"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>

      <div class="form-group">
        <label for="licenseNumber" class="block text-sm font-medium text-gray-700">License Number</label>
        <input
          type="text"
          id="licenseNumber"
          formControlName="licenseNumber"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
      </div>
    </div>

    <div class="mt-6 flex justify-end space-x-4">
      <button
        type="button"
        (click)="router.navigate(['/doctors'])"
        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
      >
        Cancel
      </button>
      <button
        type="submit"
        [disabled]="!doctorForm.valid"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
      >
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div> 