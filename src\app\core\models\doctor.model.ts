export interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specialization: string;
  licenseNumber: string;
  education: string;
  experience: number;
  availableDays: string[];
  startTime: string;
  endTime: string;
  createdAt: string;
  updatedAt?: string;
}

export interface DoctorListResponse {
  doctors: Doctor[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
}

export interface DoctorRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specialization: string;
  licenseNumber: string;
  education: string;
  experience: number;
  availableDays: string[];
  startTime: string;
  endTime: string;
} 