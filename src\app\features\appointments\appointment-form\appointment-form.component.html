<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-6">{{ isEditMode ? 'Edit' : 'Create' }} Appointment</h1>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700">Patient</label>
        <select formControlName="patientId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
          <option value="">Select a patient</option>
          <option *ngFor="let patient of patients" [value]="patient.id">
            {{ patient.firstName }} {{ patient.lastName }}
          </option>
        </select>
        <div *ngIf="appointmentForm.get('patientId')?.invalid && appointmentForm.get('patientId')?.touched" class="text-red-500 text-sm mt-1">
          Please select a patient
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Doctor</label>
        <select formControlName="doctorId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
          <option value="">Select a doctor</option>
          <option *ngFor="let doctor of doctors" [value]="doctor.id">
            Dr. {{ doctor.firstName }} {{ doctor.lastName }} ({{ doctor.specialization }})
          </option>
        </select>
        <div *ngIf="appointmentForm.get('doctorId')?.invalid && appointmentForm.get('doctorId')?.touched" class="text-red-500 text-sm mt-1">
          Please select a doctor
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Date</label>
        <input type="date" formControlName="date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="appointmentForm.get('date')?.invalid && appointmentForm.get('date')?.touched" class="text-red-500 text-sm mt-1">
          Please select a date
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Start Time</label>
        <input type="time" formControlName="startTime" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="appointmentForm.get('startTime')?.invalid && appointmentForm.get('startTime')?.touched" class="text-red-500 text-sm mt-1">
          Please select a start time
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">End Time</label>
        <input type="time" formControlName="endTime" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="appointmentForm.get('endTime')?.invalid && appointmentForm.get('endTime')?.touched" class="text-red-500 text-sm mt-1">
          Please select an end time
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Status</label>
        <select formControlName="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
          <option *ngFor="let status of AppointmentStatus | keyvalue" [value]="status.value">
            {{ status.value }}
          </option>
        </select>
        <div *ngIf="appointmentForm.get('status')?.invalid && appointmentForm.get('status')?.touched" class="text-red-500 text-sm mt-1">
          Please select a status
        </div>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">Notes</label>
        <textarea formControlName="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
      </div>
    </div>

    <div class="flex justify-end space-x-4 mt-6">
      <button type="button" (click)="router.navigate(['/appointments'])" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Cancel
      </button>
      <button type="submit" [disabled]="appointmentForm.invalid || loading" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
        <span *ngIf="loading" class="inline-block animate-spin mr-2">⟳</span>
        {{ isEditMode ? 'Update' : 'Create' }} Appointment
      </button>
    </div>
  </form>
</div> 