export interface Appointment {
  id: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  status: AppointmentStatus;
  type: string;
  notes?: string;
  createdAt: string;
  updatedAt?: string;
}

export enum AppointmentStatus {
  Scheduled = 'Scheduled',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
  NoShow = 'No-Show'
}

export interface AppointmentRequest {
  patientId: string;
  doctorId: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  type: string;
  notes?: string;
}

export interface AppointmentListResponse {
  appointments: Appointment[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
} 