import { Routes } from '@angular/router';

export const DOCTORS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./doctor-list/doctor-list.component').then(m => m.DoctorListComponent)
  },
  {
    path: 'add',
    loadComponent: () => import('./doctor-form/doctor-form.component').then(m => m.DoctorFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./doctor-form/doctor-form.component').then(m => m.DoctorFormComponent)
  },
  {
    path: ':id',
    loadComponent: () => import('./doctor-detail/doctor-detail.component').then(m => m.DoctorDetailComponent)
  }
]; 